"""
适应度评估模块 - 重构版本

本模块实现了机场滑行路径的适应度评估功能，包括：
1. 路径解码和修复
2. 段处理和分类
3. 时间窗计算
4. 适应度计算
5. 冲突检测和解决
6. 并行种群评估
"""

import math
import numpy as np
from Decoding import decode_individual
import copy
from multiprocessing import Pool, cpu_count
from functools import partial
import warnings

# 导入配置参数
from config import AIRCRAFT_PARAMETERS, SEGMENT_TYPE_SPEEDS, PARALLEL_CONFIG


# 保留原始函数以保持向后兼容性

def calculate_speed_profile_from_m2(m2_value):
    """
    根据M2值计算speed_profile

    M2值在[0,1]范围内，映射到两级速度配置：
    - [0, 0.5): speed_profile = 1 (保守/经济模式：匀速滑行，燃油优先)
    - [0.5, 1.0]: speed_profile = 2 (激进/时间模式：加速-匀速-减速，时间优先)

    注意：根据doh1_database.csv数据文件验证，只存在speed_profile=1和2两种配置

    参数:
        m2_value: M2编码值，范围[0,1]

    返回:
        speed_profile: 速度配置编号 (1 或 2)
    """
    if m2_value < 0.5:
        return 1  # 保守/经济模式
    else:
        return 2  # 激进/时间模式


def find_segment_config_with_tolerance(speed_profile_df, weight_type, segment_type, segment_length, speed_profile):
    """
    使用多层次容忍度策略查找段配置

    参数:
        speed_profile_df: 速度配置数据框
        weight_type: 飞机重量类型
        segment_type: 段类型
        segment_length: 段长度
        speed_profile: 速度配置

    返回:
        匹配的配置DataFrame
    """
    import pandas as pd
    import numpy as np

    # 策略1: 精确匹配（容忍度0.01）
    segment_info = speed_profile_df[
        (speed_profile_df['aircraft_weight_class'] == weight_type) &
        (speed_profile_df['segment_type'] == segment_type) &
        (np.isclose(speed_profile_df['segment_length'], segment_length, atol=0.01)) &
        (speed_profile_df['speed_profile'] == speed_profile)
    ]

    if not segment_info.empty:
        return segment_info

    # 策略2: 增加容忍度到1.0米
    segment_info = speed_profile_df[
        (speed_profile_df['aircraft_weight_class'] == weight_type) &
        (speed_profile_df['segment_type'] == segment_type) &
        (np.isclose(speed_profile_df['segment_length'], segment_length, atol=1.0)) &
        (speed_profile_df['speed_profile'] == speed_profile)
    ]

    if not segment_info.empty:
        return segment_info

    # 策略3: 找最接近的长度（相同类型和速度配置）
    same_type_configs = speed_profile_df[
        (speed_profile_df['aircraft_weight_class'] == weight_type) &
        (speed_profile_df['segment_type'] == segment_type) &
        (speed_profile_df['speed_profile'] == speed_profile)
    ]

    if not same_type_configs.empty:
        # 找到最接近的长度
        length_diff = np.abs(same_type_configs['segment_length'] - segment_length)
        closest_idx = length_diff.idxmin()
        return same_type_configs.loc[[closest_idx]]

    # 策略4: 简化段类型（去掉特殊标记）
    simplified_type = 'straight' if 'straight' in segment_type else ('turning' if 'turning' in segment_type else segment_type)
    if simplified_type != segment_type:
        segment_info = speed_profile_df[
            (speed_profile_df['aircraft_weight_class'] == weight_type) &
            (speed_profile_df['segment_type'] == simplified_type) &
            (np.isclose(speed_profile_df['segment_length'], segment_length, atol=1.0)) &
            (speed_profile_df['speed_profile'] == speed_profile)
        ]

        if not segment_info.empty:
            return segment_info

        # 如果简化类型还是没找到，找最接近的长度
        simplified_configs = speed_profile_df[
            (speed_profile_df['aircraft_weight_class'] == weight_type) &
            (speed_profile_df['segment_type'] == simplified_type) &
            (speed_profile_df['speed_profile'] == speed_profile)
        ]

        if not simplified_configs.empty:
            length_diff = np.abs(simplified_configs['segment_length'] - segment_length)
            closest_idx = length_diff.idxmin()
            return simplified_configs.loc[[closest_idx]]

    # 策略5: 使用默认配置生成
    return generate_default_config(weight_type, segment_type, segment_length, speed_profile)


def generate_default_config(weight_type, segment_type, segment_length, speed_profile):
    """
    生成默认配置

    参数:
        weight_type: 飞机重量类型
        segment_type: 段类型
        segment_length: 段长度
        speed_profile: 速度配置

    返回:
        默认配置DataFrame
    """
    import pandas as pd

    # 基于物理模型生成默认值
    aircraft_params = AIRCRAFT_PARAMETERS.get(weight_type, AIRCRAFT_PARAMETERS['Medium'])

    # 简化段类型
    simplified_type = 'straight' if 'straight' in segment_type else ('turning' if 'turning' in segment_type else segment_type)

    if simplified_type == 'turning':
        # 转弯段：匀速通过
        a1, d1, d2, d4 = 0.0, 0.0, segment_length, 0.0
        g1 = segment_length / 5.14  # 使用标准转弯速度
        g2 = g1 * aircraft_params['fuel_flow_7']
    else:
        # 直线段：使用标准参数
        a1 = 0.98
        d1 = min(segment_length * 0.2, 20.0)
        d4 = min(segment_length * 0.2, 20.0)
        d2 = max(0, segment_length - d1 - d4)

        # 计算时间和燃油
        v_cruise = 5.14
        v0, v4 = 0.0, 0.0  # 默认起始和结束速度
        t1 = (v_cruise - v0) / a1 if a1 > 0 else 0
        t2 = d2 / v_cruise if v_cruise > 0 else 0
        t4 = (v_cruise - v4) / a1 if a1 > 0 else 0
        g1 = t1 + t2 + t4
        g2 = g1 * aircraft_params['fuel_flow_30']

    # 创建默认配置DataFrame
    default_config = pd.DataFrame([{
        'aircraft_weight_class': weight_type,
        'segment_type': segment_type,
        'segment_length': segment_length,
        'speed_profile': speed_profile,
        'a1': a1,
        'd1': d1,
        'd2': d2,
        'd4': d4,
        'g1': g1,
        'g2': g2
    }])

    return default_config


def repair_path_encoding_improved(evaluate_individual, path_with_m2, node_vector, min_path_length, minHop):
    """
    改进的路径编码修复策略

    保持编码值的相对关系，避免破坏路径选择的逻辑

    参数:
        evaluate_individual: 个体编码
        path_with_m2: 解码后的路径
        node_vector: 节点向量
        min_path_length: 最短路径长度字典
        minHop: 当前最小跳数

    返回:
        修复后的个体编码
    """
    repaired_individual = copy.deepcopy(evaluate_individual)

    # 计算所有节点的M1值调整量
    max_adjustment = 0
    for node, _ in path_with_m2:
        if node in node_vector:
            node_min_hop = min_path_length[node]
            if node_min_hop > max_adjustment:
                max_adjustment = node_min_hop

    # 统一调整所有节点的M1值，保持相对关系
    if max_adjustment > 0:
        # 首先找到最小的M1值，确保调整后所有值都为正
        min_m1 = min(repaired_individual[i][0] for i in range(1, len(repaired_individual))
                     if isinstance(repaired_individual[i], tuple))

        # 计算需要的最小调整量，确保所有M1值都为正
        min_adjustment_needed = max(0, -min_m1 + 1)  # +1确保严格大于0
        total_adjustment = max(max_adjustment, min_adjustment_needed)

        for i in range(1, len(repaired_individual)):  # 跳过时间编码
            if isinstance(repaired_individual[i], tuple):
                current_m1, current_m2 = repaired_individual[i]
                # 统一增加调整量，保持相对关系
                new_m1 = current_m1 + total_adjustment
                repaired_individual[i] = (new_m1, current_m2)

    return repaired_individual
def calculate_angle(edge1, edge2, nodes_df):
    """计算两条边之间的夹角"""
    import math
    
    # 获取第一条边的端点坐标
    start1 = edge1['Start Node'].values[0]
    end1 = edge1['End Node'].values[0]
    
    # 获取第二条边的端点坐标
    start2 = edge2['Start Node'].values[0]
    end2 = edge2['End Node'].values[0]
    
    # 从nodes_df获取节点坐标
    def get_node_coords(node_id):
        node_row = nodes_df[nodes_df['Node ID'] == node_id]
        if node_row.empty:
            return 0, 0
        return float(node_row['X'].values[0]), float(node_row['Y'].values[0])
    
    x1, y1 = get_node_coords(start1)
    x2, y2 = get_node_coords(end1)
    x3, y3 = get_node_coords(start2)
    x4, y4 = get_node_coords(end2)
    
    # 计算两条边的方向向量
    vec1 = (x2 - x1, y2 - y1)
    vec2 = (x4 - x3, y4 - y3)
    
    # 计算向量的模长
    len1 = math.sqrt(vec1[0]**2 + vec1[1]**2)
    len2 = math.sqrt(vec2[0]**2 + vec2[1]**2)
    
    if len1 == 0 or len2 == 0:
        return 0
    
    # 计算向量的点积
    dot_product = vec1[0] * vec2[0] + vec1[1] * vec2[1]
    
    # 计算夹角的余弦值
    cos_angle = dot_product / (len1 * len2)
    
    # 限制余弦值范围在[-1, 1]
    cos_angle = max(-1, min(1, cos_angle))
    
    # 计算夹角（弧度）并转换为角度
    angle_rad = math.acos(cos_angle)
    angle_deg = math.degrees(angle_rad)
    
    return angle_deg


def calculate_time_window_conflicts(airport_G, edge_time_windows):
    """计算时间窗冲突（保持向后兼容）"""
    conflicts = 0
    conflicts_edge_time = []

    for (node1, node2), (t1, t2) in edge_time_windows:
        if (node1, node2) in airport_G.edges:
            time_windows = airport_G.edges[node1, node2].get('unavailable_time_windows', [])
            delay_required = 0
            if time_windows:
                for window_start, window_end in time_windows:
                    if not (t2 < window_start or t1 > window_end):
                        conflicts += 1
                        dt = window_end - t1
                        t1 = t1 + dt
                        t2 = t2 + dt
                        delay_required += dt

            if delay_required > 0:
                conflicts_edge_time.append(((node1, node2), delay_required))

    return conflicts, conflicts_edge_time


def evaluate_path(evaluate_individual, aircraft, aircraft_subG, node_vector, min_path_length, nodes_df, edges_df, speed_profile_df, maxCost, G1):
    """
    评估单个飞机路径
    
    参数:
        evaluate_individual: 个体编码
        aircraft: 飞机信息
        aircraft_subG: 飞机子图
        node_vector: 节点向量
        min_path_length: 最短路径长度字典
        nodes_df: 节点数据框
        edges_df: 边数据框
        speed_profile_df: 速度配置数据框
        maxCost: 最大成本
        G1: 机场图
        
    返回:
        total_g1: 总滑行时间
        total_g2: 总燃油消耗
        edge_time_windows: 边时间窗
        constraint1: 路径约束违反
        constraint2: 时间窗约束违反
        conflicts_edge_time: 冲突边时间
        evaluate_individual: 修复后的个体编码
    """
    all_lengths = []
    all_segments = []
    total_g1 = 0
    total_g2 = 0

    # 检查传入参数的类型
    start_node = aircraft['Start Node']
    TURE_END_NODE = aircraft['End Node']
    start_time = aircraft['Start Time'] + evaluate_individual[0]
    weight_class = aircraft['Weight Class']
    # 解码
    path_with_m2 = decode_individual(copy.deepcopy(evaluate_individual[1:]), aircraft_subG, node_vector, start_node, TURE_END_NODE)  # 解码
    last_node = path_with_m2[-1][0]  # 获取 path_with_m2 的终点节点
    minHop = min_path_length[last_node]  # 使用 last_node 获取最小路径长度

    # 如果 minHop > 0，表示非通路，进行修复
    if minHop > 0:
        # 修复：改进M1值修复策略，保持编码相对关系
        evaluate_individual = repair_path_encoding_improved(
            evaluate_individual, path_with_m2, node_vector, min_path_length, minHop
        )

        # 修复后重新解码路径
        path_with_m2 = decode_individual(evaluate_individual[1:], aircraft_subG, node_vector, start_node, TURE_END_NODE)
        # 更新解码后的路径信息
        last_node = path_with_m2[-1][0]  # 更新解码后的终点节点
        minHop = min_path_length[last_node]  # 更新 minHop 值

    # 根据 weight_class 查找对应的飞机类型参数
    if weight_class == '1':
        weight_type = 'light'
    elif weight_class == '2':
        weight_type = 'Medium'
    elif weight_class == '3':
        weight_type = 'Heavy'
    else:
        raise ValueError("Invalid weight_class")

    fuel_flow_7 = AIRCRAFT_PARAMETERS[weight_type]['fuel_flow_7']  # 用于turning segment 的燃油流量

    # 继续原始函数的其余部分...
    start_node = path_with_m2[0][0]  # 起点
    end_node = path_with_m2[-1][0]  # 终点

    # 计算各条边的类型
    path_edges = []  # 存储每条边的信息
    prev_edge = None  # 初始化prev_edge
    for i in range(1, len(path_with_m2)):
        # 查找当前边
        current_edge = edges_df.loc[((edges_df['Start Node'] == path_with_m2[i - 1][0]) &
                                      (edges_df['End Node'] == path_with_m2[i][0])) |
                                     ((edges_df['Start Node'] == path_with_m2[i][0]) &
                                      (edges_df['End Node'] == path_with_m2[i - 1][0]))]

        length = current_edge['Length'].values[0]  # 当前edge的长度

        # 计算夹角
        if i > 1 and prev_edge is not None:
            angle = calculate_angle(prev_edge, current_edge, nodes_df)  # 计算夹角
            edge_type = 'turning' if angle > 30 else 'straight'
        else:
            edge_type = 'straight'  # 第一个边默认为straight

        # 将边的类型、长度和端点记录在path_edges中
        path_edges.append((edge_type, length, path_with_m2[i - 1][0], path_with_m2[i][0]))

        prev_edge = current_edge  # 更新prev_edge

    # 特殊处理最后一条边
    if path_with_m2[-1][0] != TURE_END_NODE:
        # 判断最后一条边是不是 turning edge
        last_edge_type = path_edges[-1][0]

        if last_edge_type != 'turning':
            # 从 path_edges 中逆序搜索最后一个 'turning' edge 的索引
            last_turning_index = next((i for i in reversed(range(len(path_edges))) if path_edges[i][0] == 'turning'),
                                      None)

            # 如果找到最后一个 turning edge，删减到此位置
            if last_turning_index is not None:
                # 计算删减的节点数量
                nodes_removed = len(path_edges) - last_turning_index
                minHop += nodes_removed  # 更新 minHop

                # 更新 path_edges 和 path_with_m2，删减到最后一个 turning edge 之后的部分
                path_edges = path_edges[:last_turning_index+1]
                path_with_m2 = path_with_m2[:last_turning_index + 2]  # 因为 path_with_m2 是节点，索引要 +2

                # 更新起点和终点
                start_node = path_with_m2[0][0]  # 起点
                end_node = path_with_m2[-1][0]  # 终点

            # 如果没有找到 turning edge，全部删除并更新 minHop
            else:
                nodes_removed = len(path_edges)
                minHop += nodes_removed
                path_edges = []
                path_with_m2 = []

    else:
        # 如果最后节点是 TURE_END_NODE，则设置最后一条边为 straight
        path_edges[-1] = ('straight', path_edges[-1][1], path_edges[-1][2], path_with_m2[-1][0])

    # 合并相同类型的边到段（segment）中
    if path_edges:
        current_segment_type = path_edges[0][0]  # 初始化为第一个边的类型
        current_segment_length = 0
        current_segment_start = path_edges[0][2]  # 段的起点
        current_segment_end = path_edges[0][3]  # 段的终点
        current_segment_start_m2 = path_with_m2[0][1]  # 获取起点的 M2 值
        current_segment_nodes = [current_segment_start]  # 储存当前segment包含的所有节点
        current_segment_lengths = []  # 储存当前segment中每条边的长度

        first_straight_segment = True  # 用来标记是否是第一个 straight segment

        for i, (edge_type, _, start, end) in enumerate(path_edges):
            # 根据 start 和 end 查找对应的边长度
            edge_info = edges_df[(edges_df['Start Node'] == start) & (edges_df['End Node'] == end) |
                                    (edges_df['Start Node'] == end) & (edges_df['End Node'] == start)]

            if edge_info.empty:
                raise ValueError(f"No edge found between {start} and {end} in edge_df")

            # 提取边的长度
            length = edge_info['Length'].values[0]

            if current_segment_type == edge_type:
                current_segment_length += length
                current_segment_end = end  # 更新段的终点
                current_segment_nodes.append(end)  # 添加edge的终点到segment的节点列表
                current_segment_lengths.append(length)  # 添加edge的长度到segment的边长列表
            else:
                # 保存前一个段
                if current_segment_type == 'straight':
                    # 第一个straight段，如果起点是start_node，标记为 straight breakaway
                    if first_straight_segment and current_segment_start == start_node:
                        current_segment_type = 'straight breakaway'
                        first_straight_segment = False  # 标记处理过第一个straight段
                    # 最后一个straight段，如果终点是end_node，标记为 straight holding
                    elif current_segment_end == end_node:
                        current_segment_type = 'straight holding'
                    else:
                        first_straight_segment = False  # 之后的straight段，不再需要特殊标记

                # 记录segment的类型、长度、起点、终点、包含的节点及其M2值，还有每条边的长度
                all_lengths.append((
                    current_segment_type,
                    float(current_segment_length),
                    current_segment_start,
                    current_segment_end,
                    current_segment_start_m2,
                    current_segment_nodes,
                    current_segment_lengths  # Saving the list of edge lengths
                ))

                all_segments.append((
                    current_segment_type,
                    round(current_segment_length, 2),
                    current_segment_start,
                    current_segment_end,
                    current_segment_start_m2,
                    current_segment_nodes,
                    current_segment_lengths  # 保存边长列表
                ))

                # 更新为新段
                current_segment_type = edge_type
                current_segment_length = length
                current_segment_start = start  # 更新新的段的起点
                current_segment_end = end  # 更新新的段的终点
                current_segment_start_m2 = path_with_m2[i][1]  # 更新起点对应的M2值
                current_segment_nodes = [current_segment_start, current_segment_end]  # 新段的节点列表
                current_segment_lengths = [length]  # 新段的边长列表

        # 处理最后一个段，如果终点是end_node，标记为 straight holding
        if current_segment_end == TURE_END_NODE:
            current_segment_type = 'straight holding'

        # Appending the segment to the length list
        all_lengths.append((
            current_segment_type,
            float(current_segment_length),
            current_segment_start,
            current_segment_end,
            current_segment_start_m2,
            current_segment_nodes,
            current_segment_lengths  # Saving the list of edge lengths
        ))

        all_segments.append((
            current_segment_type,
            round(current_segment_length, 2),
            current_segment_start,
            current_segment_end,
            current_segment_start_m2,
            current_segment_nodes,
            current_segment_lengths  # 保存边长列表
        ))

    # 路段类别表，不同路段类别的起始、结束和最大速度，速度单位为米每秒（m/s）
    segment_type_speeds = {
        'straight breakaway': (0, 5.14, 15.43),  # 起始速度0，结束速度5.14 m/s
        'straight holding': (5.14, 0, 15.43),  # 起始速度5.14，结束速度0 m/s
        'straight': (5.14, 5.14, 15.43),  # 起始速度和结束速度均为5.14 m/s
        'turning': (5.14, 5.14, 5.14)  # 转弯段，恒定速度
    }

    def calculate_time_for_edges(a1, d1, d2, v0, v4, edges, current_time):
        a4 = 0.98
        times = []  # 存储每条边末端的时间和时间窗
        total_distance = 0  # 已经行驶的总距离
        total_time = current_time  # 使用输入的 current_time 作为初始时间

        # 第一步：计算加速阶段的时间（从 v0 加速到 v1）
        v1 = math.sqrt(v0 ** 2 + 2 * a1 * d1)  # 计算加速阶段末端速度 v1
        if a1 == 0:
            t1 = 0
        else:
            t1 = (v1 - v0) / a1  # 计算加速阶段所需的时间

        # 第二步：计算减速阶段的时间（从 v1 减速到 v4）
        t4 = (v1 - v4) / a4  # 计算减速阶段所需的时间 (这个阶段可能为0)

        # 设置一个小的容忍误差值 epsilon
        epsilon = 1e-6  # 可以根据需要调整这个值

        # 第三步：计算匀速行驶阶段的时间
        t2 = d2 / v1  # 计算匀速阶段所需的时间

        # 遍历每条边，计算该边的时间窗
        for edge in edges:
            t_start = total_time  # 当前边的起始时间

            # 累加当前边的长度到总距离
            total_distance += edge

            # 判断总距离是否进入加速阶段
            if total_distance <= d1 + epsilon:  # 加速阶段，容忍误差
                t_edge = (math.sqrt(v0 ** 2 + 2 * a1 * total_distance) - v0) / a1
            elif total_distance <= (d1 + d2 + epsilon):  # 匀速阶段，容忍误差
                t_edge = t1 + (total_distance - d1) / v1
            else:  # 减速阶段
                remaining_distance = total_distance - (d1 + d2)  # 计算剩余距离

                # 检查平方根计算前的值是否为负
                remaining_velocity_squared = v1 ** 2 - 2 * a4 * remaining_distance
                if remaining_velocity_squared < 0:
                    remaining_velocity_squared = 0  # 防止负值传入 sqrt 函数

                # 计算减速阶段所需时间
                if remaining_distance > 0:
                    t_edge = t1 + t2 + (v1 - math.sqrt(remaining_velocity_squared)) / a4
                else:
                    # 如果剩余距离为负，表示已经完成了减速，直接返回时间
                    t_edge = t1 + t2   # 匀速阶段时间 + 加速阶段时间

            t_edge = t_edge - (t_start - current_time)
            t_end = total_time + t_edge  # 计算当前边的结束时间

            # 存储当前边的时间窗（起始时间，结束时间）
            times.append((t_start, t_end))
            total_time = t_end  # 更新总时间，以便下一个边使用

        return times

    # 初始化用于存储每条边的时间窗
    edge_time_windows = []

    # 设置初始current_time为start_time
    current_time = start_time

    # 遍历每个segment并计算 g1 和 g2
    for segment in all_segments:
        segment_type, segment_length, segment_start, segment_end, segment_m2, segment_nodes, segment_lengths = segment

        # 获取速度曲线参数
        v0, v4, vmax = segment_type_speeds[segment_type]

        if segment_type == 'turning':
            # Turning segment: 匀速通过，每条边的时间窗为 length / 5.14m/s
            constant_speed = 5.14  # 恒定速度
            edge_time_window = calculate_time_for_edges(0, 0, segment_length, 5.14, 5.14, segment_lengths, current_time)
            for node1, node2, (t1, t2) in zip(segment_nodes[:-1], segment_nodes[1:], edge_time_window):
                edge_time_windows.append(((node1, node2), (t1, t2)))  # 保存时间窗

            g1 = segment_length / constant_speed  # 计算 g1
            g2 = g1 * fuel_flow_7  # 计算 g2

        else:
            # Straight segment: 根据 M2 值和其他参数查找 speed_profile_df 中的 g1 和 g2
            # 修复：正确使用M2值计算speed_profile（两级配置）
            speed_profile = calculate_speed_profile_from_m2(segment_m2)

            # 修复：使用改进的容忍度搜索策略
            segment_info = find_segment_config_with_tolerance(
                speed_profile_df, weight_type, segment_type, segment_length, speed_profile
            )

            g1 = segment_info['g1'].values[0]
            g2 = segment_info['g2'].values[0]
            a1 = segment_info['a1'].values[0]
            d1 = segment_info['d1'].values[0]
            d2 = segment_info['d2'].values[0]
            d4 = segment_info['d4'].values[0]

            edge_time_window = calculate_time_for_edges(a1, d1, d2, v0, v4, segment_lengths, current_time)
            for node1, node2, (t1, t2) in zip(segment_nodes[:-1], segment_nodes[1:], edge_time_window):
                edge_time_windows.append(((node1, node2), (t1, t2)))  # 保存时间窗

        # 更新 current_time 为当前 segment 的结束时间，即最后一个时间窗的 t_end
        current_time = edge_time_window[-1][1]

        # 累加 g1 和 g2
        total_g1 += g1
        total_g2 += g2

    # 惩罚计算部分
    constraint1 = 0
    constraint2 = 0

    # 时间窗约束违反判断
    conflicts, conflicts_edge_time = calculate_time_window_conflicts(G1, edge_time_windows)

    # 如果minHop大于0，加惩罚
    if minHop > 0:
        constraint1 = 1
        total_g1 += maxCost['max_time'] * minHop
        total_g2 += maxCost['max_fuel'] * minHop

    # 如果conflicts大于0，加惩罚
    if conflicts > 0:
        constraint2 = 1

    return total_g1, total_g2, edge_time_windows, constraint1, constraint2, conflicts_edge_time, evaluate_individual


def evaluate_population(local_population, aircraft_df, aircraft_subgraphs, node_vectors, min_path_lengths, nodes_df, edges_df, speed_profile_df, maxCost, G):
    """
    并行评估种群（重构版本）

    使用并行计算来加速种群评估过程，同时保持与原始接口的兼容性
    """
    if PARALLEL_CONFIG['enable_parallel'] and len(local_population) > PARALLEL_CONFIG['chunk_size']:
        return _evaluate_population_parallel(
            local_population, aircraft_df, aircraft_subgraphs, node_vectors,
            min_path_lengths, nodes_df, edges_df, speed_profile_df, maxCost, G
        )
    else:
        return _evaluate_population_sequential(
            local_population, aircraft_df, aircraft_subgraphs, node_vectors,
            min_path_lengths, nodes_df, edges_df, speed_profile_df, maxCost, G
        )


def _evaluate_population_parallel(local_population, aircraft_df, aircraft_subgraphs, node_vectors, min_path_lengths, nodes_df, edges_df, speed_profile_df, maxCost, G):
    """并行评估种群"""
    from multiprocessing import Pool

    # 确定进程数
    max_workers = PARALLEL_CONFIG['max_workers'] or min(cpu_count(), len(local_population))
    chunk_size = max(1, len(local_population) // max_workers)

    # 分割种群
    population_chunks = [local_population[i:i + chunk_size] for i in range(0, len(local_population), chunk_size)]

    # 创建评估函数的部分应用
    eval_func = partial(
        _evaluate_chunk,
        aircraft_df=aircraft_df,
        aircraft_subgraphs=aircraft_subgraphs,
        node_vectors=node_vectors,
        min_path_lengths=min_path_lengths,
        nodes_df=nodes_df,
        edges_df=edges_df,
        speed_profile_df=speed_profile_df,
        maxCost=maxCost,
        G=copy.deepcopy(G)  # 每个进程使用独立的图副本
    )

    # 并行处理
    with Pool(processes=max_workers) as pool:
        chunk_results = pool.map(eval_func, population_chunks)

    # 合并结果
    evaluated_pop = []
    repair_pop = []

    for chunk_eval, chunk_repaired in chunk_results:
        evaluated_pop.extend(chunk_eval)
        repair_pop.extend(chunk_repaired)

    return evaluated_pop, repair_pop


def _evaluate_population_sequential(local_population, aircraft_df, aircraft_subgraphs, node_vectors, min_path_lengths, nodes_df, edges_df, speed_profile_df, maxCost, G):
    """串行评估种群（保持原始逻辑）"""
    evaluated_pop = []
    repair_pop = []
    current_G = copy.deepcopy(G)

    for local_individual in local_population:
        total_g1 = 0
        total_g2 = 0
        total_constraints = 0
        # 修复：为每个个体创建独立的图副本，避免飞机间冲突被错误清除
        individual_G = copy.deepcopy(current_G)
        # 清空当前个体的图状态
        for start, end in individual_G.edges:
            individual_G[start][end]['unavailable_time_windows'] = []
            if individual_G.has_edge(end, start):
                individual_G[end][start]['unavailable_time_windows'] = []

        # 用于保存更新后的个体
        new_individual = {}

        # Evaluate each aircraft in the individual's flight plan
        for aircraft_id, path_encoding in local_individual.items():
            aircraft = aircraft_df.loc[aircraft_id]  # Get the corresponding aircraft details
            aircraft_subgraph1 = copy.deepcopy(aircraft_subgraphs[aircraft_id])  # Subgraph for the aircraft
            node_vector = node_vectors[aircraft_id]  # Node vector for the aircraft
            min_path_length = {
                node: min_path_lengths[(aircraft_id, node)]
                for node in node_vectors[aircraft_id]
            }

            # Evaluate the path for this aircraft
            g1, g2, edge_time_windows, constraint1, constraint2, conflicts_edge_time, new_path_encoding = evaluate_path(
                copy.deepcopy(path_encoding), aircraft, aircraft_subgraph1, node_vector, copy.deepcopy(min_path_length),
                nodes_df, edges_df, speed_profile_df, maxCost, copy.deepcopy(individual_G)
            )

            # 修复：添加循环终止条件，防止无限循环
            max_repair_iterations = 10
            repair_iteration = 0
            while constraint2 > 0 and repair_iteration < max_repair_iterations:
                # 获取当前最大的延迟要求
                max_delay_required = 1 + max([delay for _, delay in conflicts_edge_time], default=0)

                # 调整路径编码（滑行开始时间增加最大延迟）
                new_path_encoding[0] += max_delay_required  # 更新开始滑行时间

                # 遍历列表中的每个元素，修改 t1, t2 时间窗
                for i in range(len(edge_time_windows)):
                    (node1, node2), (t1, t2) = edge_time_windows[i]
                    edge_time_windows[i] = ((node1, node2), (t1 + max_delay_required, t2 + max_delay_required))

                # 重新计算冲突
                conflicts, conflicts_edge_time = calculate_time_window_conflicts(individual_G, edge_time_windows)

                # 更新constraint2（如果需要，可以做一些调整）
                constraint2 = conflicts  # 重新计算约束值

                # 如果没有冲突，跳出循环
                if conflicts == 0:
                    break

                # 增加修复迭代计数器
                repair_iteration += 1

            # 最后更新g1
            g1 = g1 + new_path_encoding[0]

            # 更新到 new_individual
            new_individual[aircraft_id] = new_path_encoding

            total_constraints += constraint1 + constraint2
            total_g1 += g1  # Accumulate taxi time
            total_g2 += g2  # Accumulate fuel consumption

            def merge_time_windows(time_windows):
                """Merge overlapping or adjacent time windows."""
                if not time_windows:
                    return time_windows

                # Sort time windows by start time
                sorted_windows = sorted(time_windows, key=lambda x: x[0])
                merged_windows = [sorted_windows[0]]

                for current_start, current_end in sorted_windows[1:]:
                    last_start, last_end = merged_windows[-1]

                    # If current window overlaps or is adjacent to the last one, merge them
                    if current_start <= last_end:
                        merged_windows[-1] = (last_start, max(last_end, current_end))
                    else:
                        merged_windows.append((current_start, current_end))

                return list(merged_windows)

            # 更新个体图和全局图中的时间窗
            for (start_node, end_node), (t1, t2) in edge_time_windows:
                # 更新个体图
                if individual_G.has_edge(start_node, end_node):
                    current_time_windows = individual_G[start_node][end_node].get('unavailable_time_windows', [])
                    current_time_windows.append((t1, t2))
                    merged_time_windows = merge_time_windows(current_time_windows)
                    individual_G[start_node][end_node]['unavailable_time_windows'] = merged_time_windows

                    if individual_G.has_edge(end_node, start_node):
                        individual_G[end_node][start_node]['unavailable_time_windows'] = merged_time_windows

                # 更新全局图（累积所有飞机的时间窗）
                if current_G.has_edge(start_node, end_node):
                    current_time_windows = current_G[start_node][end_node].get('unavailable_time_windows', [])
                    current_time_windows.append((t1, t2))
                    merged_time_windows = merge_time_windows(current_time_windows)
                    current_G[start_node][end_node]['unavailable_time_windows'] = merged_time_windows

                    if current_G.has_edge(end_node, start_node):
                        current_G[end_node][start_node]['unavailable_time_windows'] = merged_time_windows

        # Store the results for this individual
        repair_pop.append(new_individual)
        evaluated_pop.append((total_g1, total_g2, total_constraints))

    return evaluated_pop, repair_pop


def _evaluate_chunk(population_chunk, aircraft_df, aircraft_subgraphs, node_vectors, min_path_lengths, nodes_df, edges_df, speed_profile_df, maxCost, G):
    """评估种群块（用于并行处理，保持原始逻辑）"""
    chunk_evaluated = []
    chunk_repaired = []
    current_G = copy.deepcopy(G)

    for local_individual in population_chunk:
        total_g1 = 0
        total_g2 = 0
        total_constraints = 0

        # 修复：为每个个体创建独立的图副本，避免飞机间冲突被错误清除
        individual_G = copy.deepcopy(current_G)
        # 清空当前个体的图状态
        for start, end in individual_G.edges:
            individual_G[start][end]['unavailable_time_windows'] = []
            if individual_G.has_edge(end, start):
                individual_G[end][start]['unavailable_time_windows'] = []

        # 用于保存更新后的个体
        new_individual = {}

        # Evaluate each aircraft in the individual's flight plan
        for aircraft_id, path_encoding in local_individual.items():
            aircraft = aircraft_df.loc[aircraft_id]  # Get the corresponding aircraft details
            aircraft_subgraph1 = copy.deepcopy(aircraft_subgraphs[aircraft_id])  # Subgraph for the aircraft
            node_vector = node_vectors[aircraft_id]  # Node vector for the aircraft
            min_path_length = {
                node: min_path_lengths[(aircraft_id, node)]
                for node in node_vectors[aircraft_id]
            }

            # Evaluate the path for this aircraft
            g1, g2, edge_time_windows, constraint1, constraint2, conflicts_edge_time, new_path_encoding = evaluate_path(
                copy.deepcopy(path_encoding), aircraft, aircraft_subgraph1, node_vector, copy.deepcopy(min_path_length),
                nodes_df, edges_df, speed_profile_df, maxCost, copy.deepcopy(individual_G)
            )

            # 修复：添加循环终止条件，防止无限循环
            max_repair_iterations = 10
            repair_iteration = 0
            while constraint2 > 0 and repair_iteration < max_repair_iterations:
                # 获取当前最大的延迟要求
                max_delay_required = 1 + max([delay for _, delay in conflicts_edge_time], default=0)

                # 调整路径编码（滑行开始时间增加最大延迟）
                new_path_encoding[0] += max_delay_required  # 更新开始滑行时间

                # 遍历列表中的每个元素，修改 t1, t2 时间窗
                for i in range(len(edge_time_windows)):
                    (node1, node2), (t1, t2) = edge_time_windows[i]
                    edge_time_windows[i] = ((node1, node2), (t1 + max_delay_required, t2 + max_delay_required))

                # 重新计算冲突
                conflicts, conflicts_edge_time = calculate_time_window_conflicts(individual_G, edge_time_windows)

                # 更新constraint2（如果需要，可以做一些调整）
                constraint2 = conflicts  # 重新计算约束值

                # 如果没有冲突，跳出循环
                if conflicts == 0:
                    break

                # 增加修复迭代计数器
                repair_iteration += 1

            # 最后更新g1
            g1 = g1 + new_path_encoding[0]

            # 更新到 new_individual
            new_individual[aircraft_id] = new_path_encoding

            total_constraints += constraint1 + constraint2
            total_g1 += g1  # Accumulate taxi time
            total_g2 += g2  # Accumulate fuel consumption

            def merge_time_windows(time_windows):
                """Merge overlapping or adjacent time windows."""
                if not time_windows:
                    return time_windows

                # Sort time windows by start time
                sorted_windows = sorted(time_windows, key=lambda x: x[0])
                merged_windows = [sorted_windows[0]]

                for current_start, current_end in sorted_windows[1:]:
                    last_start, last_end = merged_windows[-1]

                    # If current window overlaps or is adjacent to the last one, merge them
                    if current_start <= last_end:
                        merged_windows[-1] = (last_start, max(last_end, current_end))
                    else:
                        merged_windows.append((current_start, current_end))

                return list(merged_windows)

            # 更新个体图和全局图中的时间窗
            for (start_node, end_node), (t1, t2) in edge_time_windows:
                # 更新个体图
                if individual_G.has_edge(start_node, end_node):
                    current_time_windows = individual_G[start_node][end_node].get('unavailable_time_windows', [])
                    current_time_windows.append((t1, t2))
                    merged_time_windows = merge_time_windows(current_time_windows)
                    individual_G[start_node][end_node]['unavailable_time_windows'] = merged_time_windows

                    if individual_G.has_edge(end_node, start_node):
                        individual_G[end_node][start_node]['unavailable_time_windows'] = merged_time_windows

                # 更新全局图（累积所有飞机的时间窗）
                if current_G.has_edge(start_node, end_node):
                    current_time_windows = current_G[start_node][end_node].get('unavailable_time_windows', [])
                    current_time_windows.append((t1, t2))
                    merged_time_windows = merge_time_windows(current_time_windows)
                    current_G[start_node][end_node]['unavailable_time_windows'] = merged_time_windows

                    if current_G.has_edge(end_node, start_node):
                        current_G[end_node][start_node]['unavailable_time_windows'] = merged_time_windows

        # Store the results for this individual
        chunk_repaired.append(new_individual)
        chunk_evaluated.append((total_g1, total_g2, total_constraints))

    return chunk_evaluated, chunk_repaired