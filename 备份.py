
"""
适应度评估模块 - 优化版本

本模块实现了机场滑行路径的适应度评估功能，包括：
1. 路径解码和修复
2. 段处理和分类
3. 时间窗计算
4. 适应度计算
5. 冲突检测和解决
6. 并行种群评估
"""

import math
import numpy as np
import pandas as pd
import copy
from multiprocessing import Pool, cpu_count
from functools import partial

from Decoding import decode_individual
from config import AIRCRAFT_PARAMETERS, SEGMENT_TYPE_SPEEDS, PARALLEL_CONFIG


def calculate_speed_profile_from_m2(m2_value):
    """根据M2值计算speed_profile (两级配置)"""
    return 1 if m2_value < 0.5 else 2


def get_node_coords(node_id, nodes_df):
    """获取节点坐标"""
    node_row = nodes_df[nodes_df['Node ID'] == node_id]
    if node_row.empty:
        return 0, 0
    return float(node_row['X'].values[0]), float(node_row['Y'].values[0])


def calculate_angle(edge1, edge2, nodes_df):
    """计算两条边之间的夹角"""
    # 获取边的端点
    start1, end1 = edge1['Start Node'].values[0], edge1['End Node'].values[0]
    start2, end2 = edge2['Start Node'].values[0], edge2['End Node'].values[0]

    # 获取坐标
    x1, y1 = get_node_coords(start1, nodes_df)
    x2, y2 = get_node_coords(end1, nodes_df)
    x3, y3 = get_node_coords(start2, nodes_df)
    x4, y4 = get_node_coords(end2, nodes_df)

    # 计算方向向量
    vec1 = (x2 - x1, y2 - y1)
    vec2 = (x4 - x3, y4 - y3)

    # 计算向量模长
    len1 = math.sqrt(vec1[0]**2 + vec1[1]**2)
    len2 = math.sqrt(vec2[0]**2 + vec2[1]**2)

    if len1 == 0 or len2 == 0:
        return 0

    # 计算夹角
    dot_product = vec1[0] * vec2[0] + vec1[1] * vec2[1]
    cos_angle = max(-1, min(1, dot_product / (len1 * len2)))
    return math.degrees(math.acos(cos_angle))


def merge_time_windows(time_windows):
    """合并重叠或相邻的时间窗"""
    if not time_windows:
        return time_windows

    sorted_windows = sorted(time_windows, key=lambda x: x[0])
    merged_windows = [sorted_windows[0]]

    for current_start, current_end in sorted_windows[1:]:
        last_start, last_end = merged_windows[-1]
        if current_start <= last_end:
            merged_windows[-1] = (last_start, max(last_end, current_end))
        else:
            merged_windows.append((current_start, current_end))

    return merged_windows


def find_segment_config_exact_match(speed_profile_df, weight_type, segment_type, segment_length, speed_profile):
    """
    使用精确匹配策略查找段配置

    参数:
        speed_profile_df: 速度配置数据框
        weight_type: 飞机重量类型
        segment_type: 段类型
        segment_length: 段长度
        speed_profile: 速度配置

    返回:
        匹配的配置DataFrame

    注意: 此函数移除了多层次容忍度策略，只保留精确匹配
    """
    # 精确匹配策略：在数据库中查找完全匹配的配置（容忍度0.01米）
    segment_info = speed_profile_df[
        (speed_profile_df['aircraft_weight_class'] == weight_type) &
        (speed_profile_df['segment_type'] == segment_type) &
        (np.isclose(speed_profile_df['segment_length'], segment_length, atol=0.01)) &
        (speed_profile_df['speed_profile'] == speed_profile)
    ]

    if not segment_info.empty:
        return segment_info

    raise ValueError(
        f"未找到精确匹配的段配置: {weight_type}, {segment_type}, {segment_length}, {speed_profile}"
    )



def repair_path_encoding_improved(evaluate_individual, path_with_m2, node_vector, min_path_length, minHop):
    """改进的路径编码修复策略"""
    repaired_individual = copy.deepcopy(evaluate_individual)

    max_adjustment = max((min_path_length[node] for node, _ in path_with_m2 if node in node_vector), default=0)

    if max_adjustment > 0:
        min_m1 = min(repaired_individual[i][0] for i in range(1, len(repaired_individual))
                     if isinstance(repaired_individual[i], tuple))

        total_adjustment = max(max_adjustment, max(0, -min_m1 + 1))

        for i in range(1, len(repaired_individual)):
            if isinstance(repaired_individual[i], tuple):
                current_m1, current_m2 = repaired_individual[i]
                repaired_individual[i] = (current_m1 + total_adjustment, current_m2)

    return repaired_individual


def calculate_time_window_conflicts(airport_G, edge_time_windows):
    """计算时间窗冲突"""
    conflicts = 0
    conflicts_edge_time = []

    for (node1, node2), (t1, t2) in edge_time_windows:
        if (node1, node2) in airport_G.edges:
            time_windows = airport_G.edges[node1, node2].get('unavailable_time_windows', [])
            delay_required = 0

            for window_start, window_end in time_windows:
                if not (t2 < window_start or t1 > window_end):
                    conflicts += 1
                    dt = window_end - t1
                    t1, t2 = t1 + dt, t2 + dt
                    delay_required += dt

            if delay_required > 0:
                conflicts_edge_time.append(((node1, node2), delay_required))

    return conflicts, conflicts_edge_time


def calculate_time_for_edges(a1, d1, d2, v0, v4, edges, current_time):
    """计算每条边的时间窗"""
    a4, epsilon = 0.98, 1e-6
    times, total_distance, total_time = [], 0, current_time

    v1 = math.sqrt(v0**2 + 2 * a1 * d1)
    t1 = (v1 - v0) / a1 if a1 > 0 else 0
    t2 = d2 / v1 if v1 > 0 else 0

    for edge in edges:
        t_start = total_time
        total_distance += edge

        if total_distance <= d1 + epsilon:
            t_edge = (math.sqrt(v0**2 + 2 * a1 * total_distance) - v0) / a1 if a1 > 0 else 0
        elif total_distance <= d1 + d2 + epsilon:
            t_edge = t1 + (total_distance - d1) / v1
        else:
            remaining_distance = total_distance - (d1 + d2)
            remaining_velocity_squared = max(0, v1**2 - 2 * a4 * remaining_distance)

            if remaining_distance > 0:
                t_edge = t1 + t2 + (v1 - math.sqrt(remaining_velocity_squared)) / a4
            else:
                t_edge = t1 + t2

        t_edge = t_edge - (t_start - current_time)
        t_end = total_time + t_edge
        times.append((t_start, t_end))
        total_time = t_end

    return times


def process_path_segments(path_with_m2, edges_df, nodes_df, start_node, end_node, TURE_END_NODE):
    """处理路径段，合并相同类型的边"""
    path_edges = []
    prev_edge = None

    # 计算边类型
    for i in range(1, len(path_with_m2)):
        current_edge = edges_df.loc[
            ((edges_df['Start Node'] == path_with_m2[i-1][0]) & (edges_df['End Node'] == path_with_m2[i][0])) |
            ((edges_df['Start Node'] == path_with_m2[i][0]) & (edges_df['End Node'] == path_with_m2[i-1][0]))
        ]

        length = current_edge['Length'].values[0]

        if i > 1 and prev_edge is not None:
            angle = calculate_angle(prev_edge, current_edge, nodes_df)
            edge_type = 'turning' if angle > 30 else 'straight'
        else:
            edge_type = 'straight'

        path_edges.append((edge_type, length, path_with_m2[i-1][0], path_with_m2[i][0]))
        prev_edge = current_edge

    # 特殊处理最后一条边
    if path_with_m2[-1][0] != TURE_END_NODE:
        last_turning_index = next((i for i in reversed(range(len(path_edges))) if path_edges[i][0] == 'turning'), None)

        if last_turning_index is not None:
            path_edges = path_edges[:last_turning_index+1]
            path_with_m2 = path_with_m2[:last_turning_index + 2]
        else:
            path_edges = []
            path_with_m2 = []
    else:
        if path_edges:
            path_edges[-1] = ('straight', path_edges[-1][1], path_edges[-1][2], path_with_m2[-1][0])

    # 合并相同类型的段
    if not path_edges:
        return [], path_with_m2

    segments = []
    current_segment = {
        'type': path_edges[0][0],
        'length': 0,
        'start': path_edges[0][2],
        'end': path_edges[0][3],
        'start_m2': path_with_m2[0][1],
        'nodes': [path_edges[0][2]],
        'lengths': []
    }

    first_straight_segment = True

    for i, (edge_type, length, start, end) in enumerate(path_edges):
        if current_segment['type'] == edge_type:
            current_segment['length'] += length
            current_segment['end'] = end
            current_segment['nodes'].append(end)
            current_segment['lengths'].append(length)
        else:
            # 保存前一个段，添加特殊标记
            if current_segment['type'] == 'straight':
                if first_straight_segment and current_segment['start'] == start_node:
                    current_segment['type'] = 'straight breakaway'
                    first_straight_segment = False
                elif current_segment['end'] == end_node:
                    current_segment['type'] = 'straight holding'
                else:
                    first_straight_segment = False

            segments.append((
                current_segment['type'], round(current_segment['length'], 2), current_segment['start'],
                current_segment['end'], current_segment['start_m2'], current_segment['nodes'],
                current_segment['lengths']
            ))

            # 开始新段
            current_segment = {
                'type': edge_type,
                'length': length,
                'start': start,
                'end': end,
                'start_m2': path_with_m2[i][1],
                'nodes': [start, end],
                'lengths': [length]
            }

    # 处理最后一个段
    if current_segment['end'] == TURE_END_NODE:
        current_segment['type'] = 'straight holding'

    segments.append((
        current_segment['type'], round(current_segment['length'], 2), current_segment['start'],
        current_segment['end'], current_segment['start_m2'], current_segment['nodes'],
        current_segment['lengths']
    ))

    return segments, path_with_m2


def evaluate_path(evaluate_individual, aircraft, aircraft_subG, node_vector, min_path_length, nodes_df, edges_df, speed_profile_df, maxCost, G1):
    """评估单个飞机路径"""
    total_g1 = total_g2 = 0

    # 基本信息提取
    start_node = aircraft['Start Node']
    TURE_END_NODE = aircraft['End Node']
    start_time = aircraft['Start Time'] + evaluate_individual[0]
    weight_class = aircraft['Weight Class']

    # 路径解码
    path_with_m2 = decode_individual(copy.deepcopy(evaluate_individual[1:]), aircraft_subG, node_vector, start_node, TURE_END_NODE)
    last_node = path_with_m2[-1][0]
    minHop = min_path_length[last_node]

    # 路径修复
    if minHop > 0:
        evaluate_individual = repair_path_encoding_improved(
            evaluate_individual, path_with_m2, node_vector, min_path_length, minHop
        )
        path_with_m2 = decode_individual(evaluate_individual[1:], aircraft_subG, node_vector, start_node, TURE_END_NODE)
        last_node = path_with_m2[-1][0]
        minHop = min_path_length[last_node]

    # 飞机类型映射
    weight_type_map = {'1': 'light', '2': 'Medium', '3': 'Heavy'}
    weight_type = weight_type_map.get(weight_class, 'Medium')
    fuel_flow_7 = AIRCRAFT_PARAMETERS[weight_type]['fuel_flow_7']

    # 处理路径段
    start_node = path_with_m2[0][0]
    end_node = path_with_m2[-1][0]
    all_segments, path_with_m2 = process_path_segments(path_with_m2, edges_df, nodes_df, start_node, end_node, TURE_END_NODE)

    # 计算每个段的时间和燃油
    edge_time_windows = []
    current_time = start_time

    for segment in all_segments:
        segment_type, segment_length, _, _, segment_m2, segment_nodes, segment_lengths = segment

        v0, v4, _ = SEGMENT_TYPE_SPEEDS[segment_type]

        if segment_type == 'turning':
            edge_time_window = calculate_time_for_edges(0, 0, segment_length, 5.14, 5.14, segment_lengths, current_time)
            g1 = segment_length / 5.14
            g2 = g1 * fuel_flow_7
        else:
            # 特殊规则：straight breakaway 段必须使用速度策略1，不受M2值影响
            if segment_type == 'straight breakaway':
                speed_profile = 1  # 强制设置为速度策略1
            else:
                speed_profile = calculate_speed_profile_from_m2(segment_m2)

            segment_info = find_segment_config_exact_match(
                speed_profile_df, weight_type, segment_type, segment_length, speed_profile
            )

            g1 = segment_info['g1'].values[0]
            g2 = segment_info['g2'].values[0]
            a1 = segment_info['a1'].values[0]
            d1 = segment_info['d1'].values[0]
            d2 = segment_info['d2'].values[0]

            edge_time_window = calculate_time_for_edges(a1, d1, d2, v0, v4, segment_lengths, current_time)

        # 保存时间窗
        for node1, node2, (t1, t2) in zip(segment_nodes[:-1], segment_nodes[1:], edge_time_window):
            edge_time_windows.append(((node1, node2), (t1, t2)))

        current_time = edge_time_window[-1][1]
        total_g1 += g1
        total_g2 += g2

    # 约束计算
    constraint1 = constraint2 = 0
    conflicts, conflicts_edge_time = calculate_time_window_conflicts(G1, edge_time_windows)

    if minHop > 0:
        constraint1 = 1
        total_g1 += maxCost['max_time'] * minHop
        total_g2 += maxCost['max_fuel'] * minHop

    if conflicts > 0:
        constraint2 = 1

    return total_g1, total_g2, edge_time_windows, constraint1, constraint2, conflicts_edge_time, evaluate_individual


def handle_conflict_resolution(edge_time_windows, conflicts_edge_time, individual_G):
    """处理冲突解决逻辑"""
    if not conflicts_edge_time:
        return edge_time_windows, 0

    max_delay_required = 1 + max([delay for _, delay in conflicts_edge_time], default=0)

    # 更新时间窗
    updated_windows = []
    for (node1, node2), (t1, t2) in edge_time_windows:
        updated_windows.append(((node1, node2), (t1 + max_delay_required, t2 + max_delay_required)))

    # 重新计算冲突
    conflicts, conflicts_edge_time = calculate_time_window_conflicts(individual_G, updated_windows)
    return updated_windows, conflicts


def update_graph_time_windows(edge_time_windows, individual_G, current_G):
    """更新图中的时间窗"""
    for (start_node, end_node), (t1, t2) in edge_time_windows:
        # 更新个体图
        if individual_G.has_edge(start_node, end_node):
            current_windows = individual_G[start_node][end_node].get('unavailable_time_windows', [])
            current_windows.append((t1, t2))
            merged_windows = merge_time_windows(current_windows)
            individual_G[start_node][end_node]['unavailable_time_windows'] = merged_windows

            if individual_G.has_edge(end_node, start_node):
                individual_G[end_node][start_node]['unavailable_time_windows'] = merged_windows

        # 更新全局图
        if current_G.has_edge(start_node, end_node):
            current_windows = current_G[start_node][end_node].get('unavailable_time_windows', [])
            current_windows.append((t1, t2))
            merged_windows = merge_time_windows(current_windows)
            current_G[start_node][end_node]['unavailable_time_windows'] = merged_windows

            if current_G.has_edge(end_node, start_node):
                current_G[end_node][start_node]['unavailable_time_windows'] = merged_windows


def evaluate_individual_aircraft(local_individual, aircraft_df, aircraft_subgraphs, node_vectors, min_path_lengths, nodes_df, edges_df, speed_profile_df, maxCost, individual_G, current_G):
    """评估单个个体中的所有飞机"""
    total_g1 = total_g2 = total_constraints = 0
    new_individual = {}

    for aircraft_id, path_encoding in local_individual.items():
        aircraft = aircraft_df.loc[aircraft_id]
        aircraft_subgraph1 = copy.deepcopy(aircraft_subgraphs[aircraft_id])
        node_vector = node_vectors[aircraft_id]
        min_path_length = {node: min_path_lengths[(aircraft_id, node)] for node in node_vectors[aircraft_id]}

        # 评估路径
        g1, g2, edge_time_windows, constraint1, constraint2, conflicts_edge_time, new_path_encoding = evaluate_path(
            copy.deepcopy(path_encoding), aircraft, aircraft_subgraph1, node_vector,
            copy.deepcopy(min_path_length), nodes_df, edges_df, speed_profile_df, maxCost, copy.deepcopy(individual_G)
        )

        # 冲突解决
        max_repair_iterations = 10
        repair_iteration = 0

        while constraint2 > 0 and repair_iteration < max_repair_iterations:
            max_delay_required = 1 + max([delay for _, delay in conflicts_edge_time], default=0)
            new_path_encoding[0] += max_delay_required

            edge_time_windows, conflicts = handle_conflict_resolution(edge_time_windows, conflicts_edge_time, individual_G)
            constraint2 = conflicts

            if conflicts == 0:
                break
            repair_iteration += 1

        g1 += new_path_encoding[0]
        new_individual[aircraft_id] = new_path_encoding

        total_constraints += constraint1 + constraint2
        total_g1 += g1
        total_g2 += g2

        # 更新时间窗
        update_graph_time_windows(edge_time_windows, individual_G, current_G)

    return (total_g1, total_g2, total_constraints), new_individual


def evaluate_population(local_population, aircraft_df, aircraft_subgraphs, node_vectors, min_path_lengths, nodes_df, edges_df, speed_profile_df, maxCost, G):
    """评估种群 - 统一入口"""
    if PARALLEL_CONFIG['enable_parallel'] and len(local_population) > PARALLEL_CONFIG['chunk_size']:
        return _evaluate_population_parallel(local_population, aircraft_df, aircraft_subgraphs, node_vectors,
                                           min_path_lengths, nodes_df, edges_df, speed_profile_df, maxCost, G)
    else:
        return _evaluate_population_sequential(local_population, aircraft_df, aircraft_subgraphs, node_vectors,
                                             min_path_lengths, nodes_df, edges_df, speed_profile_df, maxCost, G)


def _evaluate_population_parallel(local_population, aircraft_df, aircraft_subgraphs, node_vectors, min_path_lengths, nodes_df, edges_df, speed_profile_df, maxCost, G):
    """并行评估种群"""
    max_workers = PARALLEL_CONFIG['max_workers'] or min(cpu_count(), len(local_population))
    chunk_size = max(1, len(local_population) // max_workers)
    population_chunks = [local_population[i:i + chunk_size] for i in range(0, len(local_population), chunk_size)]

    eval_func = partial(_evaluate_chunk, aircraft_df=aircraft_df, aircraft_subgraphs=aircraft_subgraphs,
                       node_vectors=node_vectors, min_path_lengths=min_path_lengths, nodes_df=nodes_df,
                       edges_df=edges_df, speed_profile_df=speed_profile_df, maxCost=maxCost, G=copy.deepcopy(G))

    with Pool(processes=max_workers) as pool:
        chunk_results = pool.map(eval_func, population_chunks)

    evaluated_pop, repair_pop = [], []
    for chunk_eval, chunk_repaired in chunk_results:
        evaluated_pop.extend(chunk_eval)
        repair_pop.extend(chunk_repaired)

    return evaluated_pop, repair_pop


def _evaluate_population_sequential(local_population, aircraft_df, aircraft_subgraphs, node_vectors, min_path_lengths, nodes_df, edges_df, speed_profile_df, maxCost, G):
    """串行评估种群"""
    evaluated_pop, repair_pop = [], []
    current_G = copy.deepcopy(G)

    for local_individual in local_population:
        individual_G = copy.deepcopy(current_G)

        # 清空图状态
        for start, end in individual_G.edges:
            individual_G[start][end]['unavailable_time_windows'] = []
            if individual_G.has_edge(end, start):
                individual_G[end][start]['unavailable_time_windows'] = []

        evaluation_result, new_individual = evaluate_individual_aircraft(
            local_individual, aircraft_df, aircraft_subgraphs, node_vectors, min_path_lengths,
            nodes_df, edges_df, speed_profile_df, maxCost, individual_G, current_G
        )

        repair_pop.append(new_individual)
        evaluated_pop.append(evaluation_result)

    return evaluated_pop, repair_pop


def _evaluate_chunk(population_chunk, aircraft_df, aircraft_subgraphs, node_vectors, min_path_lengths, nodes_df, edges_df, speed_profile_df, maxCost, G):
    """评估种群块（并行处理用）"""
    return _evaluate_population_sequential(population_chunk, aircraft_df, aircraft_subgraphs, node_vectors,
                                         min_path_lengths, nodes_df, edges_df, speed_profile_df, maxCost, G)